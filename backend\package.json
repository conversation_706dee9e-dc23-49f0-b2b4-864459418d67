{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "server": "nodemon server.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.6", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.9", "razorpay": "^2.9.5", "stripe": "^17.6.0", "validator": "^13.12.0"}}