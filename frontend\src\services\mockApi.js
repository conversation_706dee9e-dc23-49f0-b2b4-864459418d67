// Mock API service to handle requests locally when backend is not available
import { assets, products } from '../assets/assets';

// Mock user data
const mockUsers = [
  {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    password: 'tanya2387' // In a real app, passwords would be hashed
  }
];

// Mock token
const generateToken = (userId) => {
  return `mock-token-${userId}-${Date.now()}`;
};

// Mock cart data
let mockCart = {};

// Mock orders
let mockOrders = [];

// Helper to simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API handlers
export const mockApi = {
  // User endpoints
  async register(data) {
    await delay(500);
    const { name, email, password } = data;
    
    // Check if user already exists
    const existingUser = mockUsers.find(user => user.email === email);
    if (existingUser) {
      return { success: false, message: 'User already exists' };
    }
    
    // Create new user
    const newUser = { id: mockUsers.length + 1, name, email, password };
    mockUsers.push(newUser);
    
    // Generate token
    const token = generateToken(newUser.id);
    
    return { success: true, token, user: { id: newUser.id, name, email } };
  },
  
  async login(data) {
    await delay(500);
    const { email, password } = data;
    
    // Find user
    const user = mockUsers.find(user => user.email === email && user.password === password);
    if (!user) {
      return { success: false, message: 'Invalid credentials' };
    }
    
    // Generate token
    const token = generateToken(user.id);
    
    return { success: true, token, user: { id: user.id, name: user.name, email } };
  },
  
  // Product endpoints
  async getProducts() {
    await delay(300);
    return { success: true, products };
  },
  
  // Cart endpoints
  async getCart(token) {
    await delay(300);
    // In a real app, we would validate the token and get the user's cart
    return { success: true, cartData: mockCart };
  },
  
  async addToCart(productId, size, token) {
    await delay(300);
    // In a real app, we would validate the token
    
    if (!mockCart[productId]) {
      mockCart[productId] = {};
    }
    
    if (!mockCart[productId][size]) {
      mockCart[productId][size] = 0;
    }
    
    mockCart[productId][size] += 1;
    
    return { success: true, cartData: mockCart };
  },
  
  async updateCart(productId, size, quantity, token) {
    await delay(300);
    // In a real app, we would validate the token
    
    if (!mockCart[productId]) {
      mockCart[productId] = {};
    }
    
    mockCart[productId][size] = quantity;
    
    // Remove if quantity is 0
    if (quantity === 0 && mockCart[productId][size] !== undefined) {
      delete mockCart[productId][size];
    }
    
    // Remove empty product entries
    if (Object.keys(mockCart[productId]).length === 0) {
      delete mockCart[productId];
    }
    
    return { success: true, cartData: mockCart };
  },
  
  // Order endpoints
  async placeOrder(orderData, token) {
    await delay(800);
    // In a real app, we would validate the token
    
    const newOrder = {
      id: mockOrders.length + 1,
      ...orderData,
      status: 'Placed',
      date: new Date().toISOString(),
      items: Object.keys(mockCart).map(productId => {
        const product = products.find(p => p._id === productId);
        const sizes = Object.keys(mockCart[productId]);
        
        return sizes.map(size => ({
          productId,
          name: product.name,
          price: product.price,
          image: product.image[0],
          size,
          quantity: mockCart[productId][size]
        }));
      }).flat()
    };
    
    mockOrders.push(newOrder);
    
    // Clear cart after order
    mockCart = {};
    
    return { success: true, order: newOrder };
  },
  
  async getUserOrders(token) {
    await delay(500);
    // In a real app, we would validate the token and filter orders by user
    
    return { success: true, orders: mockOrders };
  }
};

// Intercept axios requests
export const setupMockApi = (axios) => {
  // Save the original axios methods
  const originalGet = axios.get;
  const originalPost = axios.post;
  
  // Override axios.get
  axios.get = async function(url, config) {
    console.log('Mock API intercepted GET:', url);
    
    if (url.includes('/api/product/list')) {
      const response = await mockApi.getProducts();
      return { data: response };
    }
    
    // Fall back to original implementation for unhandled routes
    return originalGet.apply(this, arguments);
  };
  
  // Override axios.post
  axios.post = async function(url, data, config) {
    console.log('Mock API intercepted POST:', url, data);
    
    if (url.includes('/api/user/register')) {
      const response = await mockApi.register(data);
      return { data: response };
    }
    
    if (url.includes('/api/user/login')) {
      const response = await mockApi.login(data);
      return { data: response };
    }
    
    if (url.includes('/api/cart/get')) {
      const token = config?.headers?.token;
      const response = await mockApi.getCart(token);
      return { data: response };
    }
    
    if (url.includes('/api/cart/add')) {
      const { productId, size } = data;
      const token = config?.headers?.token;
      const response = await mockApi.addToCart(productId, size, token);
      return { data: response };
    }
    
    if (url.includes('/api/cart/update')) {
      const { productId, size, quantity } = data;
      const token = config?.headers?.token;
      const response = await mockApi.updateCart(productId, size, quantity, token);
      return { data: response };
    }
    
    if (url.includes('/api/order/place')) {
      const token = config?.headers?.token;
      const response = await mockApi.placeOrder(data, token);
      return { data: response };
    }
    
    if (url.includes('/api/order/userOrders')) {
      const token = config?.headers?.token;
      const response = await mockApi.getUserOrders(token);
      return { data: response };
    }
    
    // Fall back to original implementation for unhandled routes
    return originalPost.apply(this, arguments);
  };
};
