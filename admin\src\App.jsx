// models/Product.js
const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true // validation
  },
  price: {
    type: Number,
    required: true
  },
  image: String,
  category: String,
  stock: {
    type: Number,
    default: 10
  },
  description: String
});

const Product = mongoose.model('Product', productSchema);
module.exports = Product;
// models/Product.js
const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true // validation
  },
  price: {
    type: Number,
    required: true
  },
  image: String,
  category: String,
  stock: {
    type: Number,
    default: 10
  },
  description: String
});

const Product = mongoose.model('Product', productSchema);
module.exports = Product;
