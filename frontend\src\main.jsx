import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';
import ShopContextProvider from './context/ShopContext';
import axios from 'axios';
import { setupMockApi } from './services/mockApi';

// Set up mock API to intercept axios requests when backend is not available
setupMockApi(axios);

ReactDOM.createRoot(document.getElementById('root')).render(
    <BrowserRouter>
    <ShopContextProvider>
    <App />
    </ShopContextProvider>
    </BrowserRouter>,
);
